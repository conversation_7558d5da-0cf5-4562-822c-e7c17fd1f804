import { Test, TestingModule } from '@nestjs/testing';
import { TController } from './t.controller';

describe('TController', () => {
  let controller: TController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TController],
    }).compile();

    controller = module.get<TController>(TController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
