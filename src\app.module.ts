import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { App<PERSON><PERSON><PERSON>er, DogController } from './app.controller';
import { AppService } from './app.service';
import { TController } from './test/t/t.controller';
import { DemoModule } from './test/demo/demo.module';
import { GraphQLModule } from '@nestjs/graphql';
import { join } from 'path';
import { ApolloDriver } from '@nestjs/apollo';


@Module({
  imports: [DemoModule, GraphQLModule.forRoot({
    driver: ApolloDriver,
    autoSchemaFile: join(process.cwd(), 'src/schema.gql'), // 自动生成 schema.gql
    path: '/api/graphql', // 👈 自定义路径
    playground: true, // 开启 GraphQL Playground
  }),
    DemoModule
  ],
  controllers: [AppController, DogController, TController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(LoggerMiddleware)
      .forRoutes('*');
  }
}

const LoggerMiddleware = (req, res, next) => {
  console.log('Request...');
  next();
};
