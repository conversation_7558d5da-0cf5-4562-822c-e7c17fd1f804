{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "启动 NestJS 应用",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "program": "${workspaceFolder}/dist/main.js",
      "preLaunchTask": "tsc: build - tsconfig.json",
      "outFiles": [
        "${workspaceFolder}/dist/**/*.js"
      ],
      "console": "integratedTerminal",
      "env": {
        "NODE_ENV": "development"
      },
      "sourceMaps": true,
      "restart": true,
      "runtimeArgs": [
        "--enable-source-maps"
      ]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "启动 NestJS 应用 (调试模式)",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "program": "${workspaceFolder}/dist/main.js",
      "preLaunchTask": "tsc: build - tsconfig.json",
      "outFiles": [
        "${workspaceFolder}/dist/**/*.js"
      ],
      "console": "integratedTerminal",
      "env": {
        "NODE_ENV": "development"
      },
      "sourceMaps": true,
      "restart": true,
      "runtimeArgs": [
        "--enable-source-maps",
        "--inspect"
      ]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "启动 NestJS 应用 (开发模式 - 直接运行 TS)",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "runtimeExecutable": "node",
      "runtimeArgs": [
        "--loader",
        "ts-node/esm"
      ],
      "args": [
        "${workspaceFolder}/src/main.ts"
      ],
      "console": "integratedTerminal",
      "env": {
        "NODE_ENV": "development",
        "TS_NODE_PROJECT": "${workspaceFolder}/tsconfig.json"
      },
      "sourceMaps": true,
      "restart": true
    }
  ]
}