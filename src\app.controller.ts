import { Body, Controller, Get, Header, HttpCode, Param, Post, Query, Req, Session } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }
}


@Controller('dogs')
export class DogController {
  appService: AppService;

  constructor(appService: AppService) {
    this.appService = appService;
  }
  @Get('get')
  getDogs(@Param() params, @Query() query) {
    console.log(params, "\n", query);

    return {
      message: '626 Dogs!',
      data: [{
        name: '626',
        age: 3,
      }],
      code: 200,
    };
  }
  @HttpCode(200)
  @Post('create')
  createDog(@Body() body, @Session() session, @Req() req) {
    console.log(req, body, session);
    return 'create dog';
  }

  @Get('sayHello')
  sayHello() {
    return this.appService.getHello();
  }
}
