{"version": "2.0.0", "tasks": [{"type": "typescript", "tsconfig": "tsconfig.json", "option": "watch", "problemMatcher": ["$tsc-watch"], "group": "build", "label": "tsc: watch - tsconfig.json"}, {"type": "typescript", "tsconfig": "tsconfig.json", "problemMatcher": ["$tsc"], "group": {"kind": "build", "isDefault": true}, "label": "tsc: build - tsconfig.json"}, {"type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "label": "npm: build", "detail": "nest build"}, {"type": "shell", "command": "npm", "args": ["run", "start:dev"], "group": "build", "label": "npm: start:dev", "detail": "nest start --watch"}]}